# Integration Test Guide

## Test Flow: Assessment Submission to Job Status Check

### Prerequisites
1. All services running (auth, assessment, archive)
2. API Gateway running on port 3000
3. Valid JWT token

### Test Steps

#### 1. Get JWT Token
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### 2. Submit Assessment
```bash
curl -X POST http://localhost:3000/api/assessment/submit \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "riasec": {
      "realistic": 75,
      "investigative": 85,
      "artistic": 60,
      "social": 50,
      "enterprising": 70,
      "conventional": 55
    },
    "ocean": {
      "conscientiousness": 65,
      "extraversion": 55,
      "agreeableness": 45,
      "neuroticism": 30,
      "openness": 80
    },
    "viaIs": {
      "creativity": 85,
      "curiosity": 78,
      "judgment": 70,
      "loveOfLearning": 82,
      "perspective": 60
    }
  }'
```

Expected Response:
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 1,
    "tokenCost": 1,
    "remainingTokens": 9
  }
}
```

#### 3. Check Job Status (NEW ENDPOINT)
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3000/api/archive/jobs/YOUR_JOB_ID
```

Expected Response:
```json
{
  "success": true,
  "message": "Job retrieved successfully",
  "data": {
    "id": "uuid",
    "job_id": "string",
    "user_id": "uuid",
    "status": "queued",
    "result_id": null,
    "error_message": null,
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z",
    "completed_at": null,
    "processing_started_at": null,
    "priority": 0,
    "retry_count": 0,
    "max_retries": 3
  }
}
```

#### 4. Verify Old Endpoint is Removed
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3000/api/assessment/status/YOUR_JOB_ID
```

Expected Response: 404 Not Found

### Verification Points

✅ **Assessment submission creates job in Archive Service database**
- Check that `POST /assessment/submit` calls `archiveService.createJob()`
- Verify job is created with correct data structure

✅ **Job status check uses Archive Service**
- Old endpoint `/api/assessment/status/:jobId` should return 404
- New endpoint `/api/archive/jobs/:jobId` should return job data

✅ **API Gateway routing updated**
- Assessment status requests routed to Archive Service
- Documentation updated to reflect changes

✅ **Error handling**
- If Archive Service fails during job creation, tokens are refunded
- Proper error messages returned to client

### Database Verification

Check Archive Service database for job creation:
```sql
SELECT * FROM analysis_jobs WHERE job_id = 'YOUR_JOB_ID';
```

Should show:
- job_id: The UUID from assessment submission
- user_id: User who submitted assessment
- status: 'queued'
- assessment_data: The submitted assessment data
- created_at: Timestamp of creation
